import logger from '../../support/utils/logger.util.ts';
import elementActions from '../../support/actions/element.actions.ts';
import path from 'path';
import { JsonTestDataHandler } from '../../support/utils/JsonTestDataHandler.ts';
import sensaFooterlinkPageObject from '../../page-object/sensa/sensa-footerlink.pageObject.ts';
import sensaLoginPageObject from '../../page-object/sensa/sensa-login.pageObject.ts';
import sensaAccountPage from './account.page.ts';
import sensaHomepagePageObject from '../../page-object/sensa/sensa-homepage.pageObject.ts';
import sensaRegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';
import assertionHelper from '../../support/helpers/assertion-helper.ts';
import camelFooterlinkPageObject from '../../page-object/camel/camel-footerlink.pageObject.ts';
import camelLoginPageObject from '../../page-object/camel/camel-login.pageObject.ts';
import camelLoginPage from '../camel/camel-login.page.ts';
import NasFooterlinkPageObject from '../../page-object/americanspirit/americanspirit-footerlink.pageObject.ts'


class FooterlinksPage {

    async faqwarningPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const contactus = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrcontactUs');
            const lblfurtherassistance = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfurtherassistance');
            const lblfurtherquestion = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfurtherquestion');
            const lblonthispage = testData.getCellValue(SHEET_NAME, scenarioname, 'lblonthispage');
            const url = await browser.getUrl();
            if ((await url).includes('camel')) {
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_ContactUs_camel, contactus);
            } else {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrfaq_ContactUs_sensa, contactus);
            }
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblfurtherassistance, lblfurtherassistance);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblfrequentQuestions, lblfurtherquestion);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblonthisPage_sensa, lblonthispage);
            const hdrwarnings = testData.getCellValue(SHEET_NAME, scenarioname, 'lblwarnings');
            const lblQuestion1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion1');
            const lblQuestion2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion2');
            const lblQuestion3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion3');
            const lblQuestion4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion4');
            const lblQuestion5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion5');
            const lblQuestion6 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion6');
            const lblQuestion7 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion7');
            const lblQuestion8 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion8');
            const lblQuestion9 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion9');
            const lblQuestion10 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion10');
            const lblQuestion11 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion11');
            const lblAnswer1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer1');
            const lblAnswer2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer2');
            const lblAnswer3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer3');
            const lblAnswer4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer4');
            const lblAnswer5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer5');
            const lblAnswer6 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer6');
            const lblAnswer7 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer7');
            const lblAnswer8 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer8');
            const lblAnswer9_1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer9_1');
            const lblAnswer9_2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer9_2');
            const lblAnswer9_3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer9_3');
            const lblAnswer9_4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer9_4');
            const lblAnswer9_5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer9_5');
            const lblAnswer9_6 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer9_6');
            const lblAnswer10_1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer10_1');
            const lblAnswer10_2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer10_2');
            const lblAnswer10_3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer10_3');
            const lblAnswer10_4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer10_4');
            const lblAnswer11 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer11');
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrfaq_warningInstructions_sensa, hdrwarnings);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ1_sensa, lblQuestion1);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA1_sensa, lblAnswer1);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ2_sensa, lblQuestion2);
            await elementActions.click(sensaFooterlinkPageObject.lblwarningQ2_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA2_sensa, lblAnswer2);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ3_sensa, lblQuestion3);
            await elementActions.click(sensaFooterlinkPageObject.lblwarningQ3_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA3_sensa, lblAnswer3);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ4_sensa, lblQuestion4);
            await elementActions.click(sensaFooterlinkPageObject.lblwarningQ4_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA4_sensa, lblAnswer4);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ5_sensa, lblQuestion5);
            await elementActions.click(sensaFooterlinkPageObject.lblwarningQ5_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA5_sensa, lblAnswer5);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ6_sensa, lblQuestion6);
            await elementActions.click(sensaFooterlinkPageObject.lblwarningQ6_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA6_sensa, lblAnswer6);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ7_sensa, lblQuestion7);
            await elementActions.click(sensaFooterlinkPageObject.lblwarningQ7_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA7_sensa, lblAnswer7);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ8_sensa, lblQuestion8);
            await elementActions.click(sensaFooterlinkPageObject.lblwarningQ8_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA8_sensa, lblAnswer8);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ9_sensa, lblQuestion9);
            await elementActions.click(sensaFooterlinkPageObject.lblwarningQ9_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA91_sensa, lblAnswer9_1);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA92_sensa, lblAnswer9_2);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA93_sensa, lblAnswer9_3);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA94_sensa, lblAnswer9_4);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA95_sensa, lblAnswer9_5);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA96_sensa, lblAnswer9_6);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ10_sensa, lblQuestion10);
            await elementActions.click(sensaFooterlinkPageObject.lblwarningQ10_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA10_1_sensa, lblAnswer10_1);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA10_2_sensa, lblAnswer10_2);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA10_3_sensa, lblAnswer10_3);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA10_4_sensa, lblAnswer10_4);
            await elementActions.click(sensaFooterlinkPageObject.lblwarningQ11_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ11_sensa, lblQuestion11);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA11_sensa, lblAnswer11);
            console.log('Validated the Content in FAQ page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in FAQ page', { error });
            throw error;
        }
    }


    async clickonfaqprefooterlink() {
        try {
            const url = await browser.getUrl();
            if ((await url).includes('camel')) {
                await elementActions.clickusingJavascript(camelFooterlinkPageObject.lnkPreFAQ_camel);
            } else if ((await url).includes('americanspirit')) {
                await elementActions.clickusingJavascript(NasFooterlinkPageObject.lnkPreFAQ_nas);
            } else if ((await url).includes('sensa')) {
                await elementActions.clickusingJavascript(sensaLoginPageObject.lnkfaq_sensa);
            }
            await this.allowpopup();
            await this.windowswitchandback();
            if ((await url).includes('camel')) {
                await elementActions.assertion(camelFooterlinkPageObject.hdrfaq_ContactUs_camel);
            } else if ((await url).includes('americanspirit')) {
                await elementActions.assertion(NasFooterlinkPageObject.contactUsHeader);
            } else if ((await url).includes('sensa')) {
                await elementActions.assertion(sensaFooterlinkPageObject.hdrfaq_ContactUs_sensa);
            }
            console.log('Navigated to FAQ Info Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to FAQ Page', { error });
            throw error;
        }
    }
    async windowswitchandback() {
        try {
            const currentWindow = await browser.getWindowHandle();
            console.log('Current Window Handle:', currentWindow);
            const allHandles = await browser.getWindowHandles();
            console.log('All Window Handles:', allHandles);
            if (!allHandles || allHandles.length < 2) {
                console.error('No additional window found to switch to.');
                return;
            }
            const childWindow = allHandles.find(handle => handle !== currentWindow);
            if (childWindow && typeof childWindow === 'string') {
                try {
                    await browser.switchToWindow(childWindow);
                    console.log('Successfully switched to child window:', childWindow);
                } catch (error) {
                    console.error('Error switching to child window:', error);
                }
            } else {
                console.error('Child window handle is invalid or undefined!');
                return;
            }
        } catch (error) {
            logger.error('Cannot switch Handles', { error });
            throw error;
        }
    }
    async allowpopup() {
        try {
            await driver.switchContext('NATIVE_APP');
            const locAllowPopUp = await sensaHomepagePageObject.lblallowpopup_sensa;
            const allowPopupDispalyed = await locAllowPopUp.isDisplayed();
            const locAllowthistimePopUp = await sensaHomepagePageObject.lblallowthistimepopup_sensa;
            const allowPopupthistimeDispalyed = await locAllowthistimePopUp.isDisplayed();

            if (allowPopupDispalyed) {
                await elementActions.click(locAllowPopUp);
            } else if (allowPopupthistimeDispalyed) {
                await elementActions.click(locAllowthistimePopUp);
            }
            const contexts = await driver.getContexts();
            const webviewContext = contexts.find(ctx => typeof ctx === 'string' && ctx.startsWith('WEBVIEW'));
            if (webviewContext) {
                await driver.switchContext(webviewContext as string);
            }
        } catch (error) {
            logger.error('Cannot switch Handles', { error });
            throw error;
        }
    }

    async faqProductPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {
            const url = driver.getUrl();
            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrproductinfo');
            const lblQuestion1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion1');
            const lblQuestion2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion2');
            const lblQuestion3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion3');
            const lblQuestion4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion4');
            const lblQuestion5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion5');
            const lblQuestion6 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion6');
            const lblQuestion7 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion7');
            const lblQuestion8 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion8');
            const lblQuestion9 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion9');
            const lblQuestion10 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion10');
            const lblQuestion11 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion11');
            const lblQuestion12 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion12');
            const lblQuestion13 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion13');
            const lblQuestion14 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion14');
            const lblQuestion15 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion15');
            const lblQuestion16 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion16');
            const lblQuestion17 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion17');
            const lblQuestion18 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion18');
            const lblQuestion19 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion19');
            const lblQuestion20 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion20');
            const lblQuestion21 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion21');
            const lblQuestion22 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion22');
            const lblQuestion23 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion23');
            const lblQuestion24 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion24');
            const lblQuestion25 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion25');
            const lblQuestion26 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion26');
            const lblQuestion27 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion27');
            const lblQuestion28 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPQuestion28');
            const lblAnswer1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer1');
            const lblAnswer2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer2');
            const lblAnswer3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer3');
            const lblAnswer4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer4');
            const lblAnswer5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer5');
            const lblAnswer6 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer6');
            const lblAnswer7 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer7');
            const lblAnswer8 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer8');
            const lblAnswer9 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer9');
            const lblAnswer10 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer10');
            const lblAnswer11 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer11');
            const lblAnswer12 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer12');
            const lblAnswer13 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer13');
            const lblAnswer14 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer14');
            const lblAnswer15 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer15');
            const lblAnswer16 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer16');
            const lblAnswer17 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer17');
            const lblAnswer18 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer18');
            const lblAnswer19 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer19');
            const lblAnswer20_1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer20_1');
            const lblAnswer20_2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer20_2');
            const lblAnswer21 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer21');
            const lblAnswer22 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer22');
            const lblAnswer23 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer23');
            const lblAnswer24 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer24');
            const lblAnswer25_1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer25_1');
            const lblAnswer25_2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer25_2');
            const lblAnswer26 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer26');
            const lblAnswer27 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer27');
            const lblAnswer28 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblPAnswer28');
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrProduct_sensa, hdrproduct);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblProductQ1_sensa, lblQuestion1);
            await elementActions.click(sensaFooterlinkPageObject.lblProductQ1_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA1_sensa, lblAnswer1);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ2_sensa, lblQuestion2);
            await elementActions.click(sensaFooterlinkPageObject.lblproductQ2_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA2_sensa, lblAnswer2);
            if ((await url).includes('aem')) {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ3_sensa, lblQuestion3);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ3_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA3_sensa, lblAnswer3);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ4_sensa, lblQuestion4);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ4_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA4_sensa, lblAnswer4);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ5_sensa, lblQuestion5);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ5_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA5_sensa, lblAnswer5);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ6_sensa, lblQuestion6);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ6_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA6_sensa, lblAnswer6);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ7_sensa, lblQuestion7);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ7_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA7_sensa, lblAnswer7);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ8_sensa, lblQuestion8);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ8_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA8_sensa, lblAnswer8);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ9_sensa, lblQuestion9);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ9_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA9_sensa, lblAnswer9);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ10_sensa, lblQuestion10);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ10_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA10_sensa, lblAnswer10);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ11_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ11_sensa, lblQuestion11);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA11_sensa, lblAnswer11);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ12_sensa, lblQuestion12);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ12_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA12_sensa, lblAnswer12);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ13_sensa, lblQuestion13);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ13_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA13_sensa, lblAnswer13);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ14_sensa, lblQuestion14);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ14_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA14_sensa, lblAnswer14);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ15_sensa, lblQuestion15);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ15_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA15_sensa, lblAnswer15);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ16_sensa, lblQuestion16);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ16_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA16_sensa, lblAnswer16);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ17_sensa, lblQuestion17);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ17_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA17_sensa, lblAnswer17);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ18_sensa, lblQuestion18);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ18_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA18_sensa, lblAnswer18);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ19_sensa, lblQuestion19);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ19_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA19_sensa, lblAnswer19);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ20_sensa, lblQuestion20);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ20_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA20_1_sensa, lblAnswer20_1);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA20_2_sensa, lblAnswer20_2);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ21_sensa, lblQuestion21);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ21_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA21_sensa, lblAnswer21);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ22_sensa, lblQuestion22);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ22_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA22_sensa, lblAnswer22);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ23_sensa, lblQuestion23);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ23_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA23_sensa, lblAnswer23);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ24_sensa, lblQuestion24);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ24_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA24_sensa, lblAnswer24);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ25_sensa, lblQuestion25);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ25_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA25_1_sensa, lblAnswer25_1);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA25_2_sensa, lblAnswer25_2);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ26_sensa, lblQuestion26);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ26_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA26_sensa, lblAnswer26);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ27_sensa, lblQuestion27);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ27_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA27_sensa, lblAnswer27);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ28_sensa, lblQuestion28);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ28_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA28_sensa, lblAnswer28);
            } else {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ3_sensa, lblQuestion28);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA3_sensa, lblAnswer28);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ4_sensa, lblQuestion3);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ4_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA4_sensa, lblAnswer3);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ5_sensa, lblQuestion4);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ5_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA5_sensa, lblAnswer4);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ6_sensa, lblQuestion5);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ6_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA6_sensa, lblAnswer5);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ7_sensa, lblQuestion6);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ7_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA7_sensa, lblAnswer6);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ8_sensa, lblQuestion7);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ8_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA8_sensa, lblAnswer7);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ9_sensa, lblQuestion8);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ9_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA9_sensa, lblAnswer8);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ10_sensa, lblQuestion9);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ10_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA10_sensa, lblAnswer9);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ11_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ11_sensa, lblQuestion10);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA11_sensa, lblAnswer10);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ12_sensa, lblQuestion11);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ12_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA12_sensa, lblAnswer11);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ13_sensa, lblQuestion12);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ13_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA13_sensa, lblAnswer12);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ14_sensa, lblQuestion13);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ14_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA14_sensa, lblAnswer13);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ15_sensa, lblQuestion14);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ15_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA15_sensa, lblAnswer14);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ16_sensa, lblQuestion15);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ16_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA16_sensa, lblAnswer15);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ17_sensa, lblQuestion16);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ17_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA17_sensa, lblAnswer16);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ18_sensa, lblQuestion17);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ18_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA18_sensa, lblAnswer17);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ19_sensa, lblQuestion18);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ19_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA19_sensa, lblAnswer18);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ20_sensa, lblQuestion19);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ20_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA20_1_sensa, lblAnswer19);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ21_sensa, lblQuestion20);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ21_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA20_2_sensa, lblAnswer20_1);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA21_sensa, lblAnswer20_2);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ22_sensa, lblQuestion21);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ22_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA22_sensa, lblAnswer21);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ23_sensa, lblQuestion22);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ23_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA23_sensa, lblAnswer22);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ24_sensa, lblQuestion23);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ24_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA23_prod_sensa, lblAnswer23);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ25_sensa, lblQuestion24);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ25_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA24_sensa, lblAnswer24);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ26_sensa, lblQuestion25);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ26_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA25_1_sensa, lblAnswer25_1);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA25_2_sensa, lblAnswer25_2);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ27_sensa, lblQuestion26);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ27_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA27_sensa, lblAnswer26);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ28_sensa, lblQuestion27);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ28_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA28_sensa, lblAnswer27);
            }
            console.log('Validated the Content in FAQ page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in FAQ page', { error });
            throw error;
        }
    }

    async faqwebsitePageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const contactus = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrcontactUs');
            const lblfurtherassistance = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfurtherassistance');
            const lblfurtherquestion = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfurtherquestion');
            const lblonthispage = testData.getCellValue(SHEET_NAME, scenarioname, 'lblonthispage');
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrfaq_ContactUs_sensa, contactus);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblfurtherassistance, lblfurtherassistance);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblfrequentQuestions, lblfurtherquestion);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblonthisPage_sensa, lblonthispage);
            const hdrwebsite = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrwebsite');
            const lblQuestion1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblWQuestion1');
            const lblQuestion2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblWQuestion2');
            const lblQuestion3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblWQuestion3');
            const lblQuestion4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblWQuestion4');
            const lblQuestion5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblWQuestion5');
            const lblQuestion6 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblWQuestion6');
            const lblAnswer1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblWAnswer1');
            const lblAnswer2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblWAnswer2');
            const lblAnswer3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblWAnswer3');
            const lblAnswer4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblWAnswer4');
            const lblAnswer5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblWAnswer5');
            const lblAnswer6 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblWAnswer6');
            const lblneedfurther = testData.getCellValue(SHEET_NAME, scenarioname, 'lblneedfurther');
            const lblcontact = testData.getCellValue(SHEET_NAME, scenarioname, 'lblourcontact');
            const lblpleasecall = testData.getCellValue(SHEET_NAME, scenarioname, 'lblpleasecall');
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrwebsite_sensa, hdrwebsite);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwebsiteQ1_sensa, lblQuestion1);
            await elementActions.click(sensaFooterlinkPageObject.lblwebsiteQ1_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwebsiteA1_sensa, lblAnswer1);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwebsiteQ2_sensa, lblQuestion2);
            await elementActions.click(sensaFooterlinkPageObject.lblwebsiteQ2_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwebsiteA2_sensa, lblAnswer2);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwebsiteQ3_sensa, lblQuestion3);
            await elementActions.click(sensaFooterlinkPageObject.lblwebsiteQ3_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwebsiteA3_sensa, lblAnswer3);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwebsiteQ4_sensa, lblQuestion4);
            await elementActions.click(sensaFooterlinkPageObject.lblwebsiteQ4_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwebsiteA4_sensa, lblAnswer4);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwebsiteQ5_sensa, lblQuestion5);
            await elementActions.click(sensaFooterlinkPageObject.lblwebsiteQ5_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwebsiteA5_sensa, lblAnswer5);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwebsiteQ6_sensa, lblQuestion6);
            await elementActions.click(sensaFooterlinkPageObject.lblwebsiteQ6_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwebsiteA6_sensa, lblAnswer6);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrneedfurther_sensa, lblneedfurther);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblourcontact_sensa, lblcontact);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblpleasecall_sensa, lblpleasecall);
            await this.footerlinkoneachpage();
            console.log('Validated the Content in FAQ page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in FAQ page', { error });
            throw error;
        }
    }

    async clickoncontactusprefooterlink() {
        try {
            await elementActions.clickusingJavascript(sensaLoginPageObject.lnkcontactUs_sensa);
            await this.allowpopup();
            await this.windowswitchandback();
            const url = await browser.getUrl();
            if ((await url).includes('camel')) {
                await elementActions.assertion(camelFooterlinkPageObject.hdrfaq_ContactUs_camel);
            } else if ((await url).includes('americanspirit')) {
                await expect(NasFooterlinkPageObject.hdrfaq_ContactUs_nas).toBeDisplayed();
            } else if ((await url).includes('sensa')) {
                await elementActions.assertion(sensaFooterlinkPageObject.hdrfaq_ContactUs_sensa);
            }
            console.log('Navigated to Contact Us Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Contact Us Page', { error });
            throw error;
        }
    }

    async contactusPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const contactus = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrcontactUs');
            const lblfurtherassistance = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfurtherassistance');
            const lblneedfurther = testData.getCellValue(SHEET_NAME, scenarioname, 'lblneedfurther');
            const lblcontact = testData.getCellValue(SHEET_NAME, scenarioname, 'lblourcontact');
            const url = await browser.getUrl();
            if ((await url).includes('camel')) {
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_ContactUs_camel, contactus);
            } else if ((await url).includes('americanspirit')) {
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.hdrfaq_ContactUs_nas, contactus);
            } else if ((await url).includes('sensa')) {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrfaq_ContactUs_sensa, contactus);
            }
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblfurtherassistance, lblfurtherassistance);
            await elementActions.click(sensaFooterlinkPageObject.lblfurtherassistance);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrneedfurther_sensa, lblneedfurther);
            if ((await url).includes('americanspirit')) {
                await elementActions.assertion(NasFooterlinkPageObject.lblHaveQuestions_nas);
                await elementActions.assertion(NasFooterlinkPageObject.listCmpText3_nas);
            } else {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblourcontact_sensa, lblcontact);
            }
            await browser.execute(() => window.history.back());
            await elementActions.assertion(sensaRegistrationPageObject.lblsignIn_sensa);
            console.log('Validated the Content in Contact us page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in Contact us page', { error });
            throw error;
        }
    }

    async footerlinkoneachpage() {
        try {
            await elementActions.assertion(sensaFooterlinkPageObject.lnkcontactus_sensa);
            await elementActions.assertion(sensaFooterlinkPageObject.lnkfaq_sensa);
            await elementActions.assertion(sensaFooterlinkPageObject.lnksitereq_sensa);
            await elementActions.assertion(sensaFooterlinkPageObject.lnktermsofuse_sensa);
            await elementActions.assertion(sensaFooterlinkPageObject.lnkprivacypolicy_sensa);
            await elementActions.assertion(sensaFooterlinkPageObject.lnktextmessaging_sensa);
            const url = driver.getUrl();
            if ((await url).includes('camel')) {
                await elementActions.assertion(sensaFooterlinkPageObject.lnkpatents_sensa);
                await elementActions.assertion(sensaFooterlinkPageObject.lnksustainability_sensa);
                await elementActions.assertion(sensaFooterlinkPageObject.lnkcamelpoints_sensa);
            } else if ((await url).includes('americanspirit')) {
                await elementActions.assertion(NasFooterlinkPageObject.lnkmsg_nas);
                await elementActions.assertion(NasFooterlinkPageObject.lnkspirit_nas);
            }
            const handles = await browser.getWindowHandles();
            console.log('Retrieved Window Handles:', handles);
            const currentWindow = await browser.getWindowHandle();
            console.log('Current Window Handle:', currentWindow);
            const parentWindow = handles.find(handle => handle !== currentWindow);
            if (!parentWindow || typeof parentWindow !== 'string') {
                console.error('Parent window handle is invalid or undefined!');
                return;
            }
            console.log('Parent Window Handle:', parentWindow);
            await browser.execute(() => {
                window.close();
            });
            try {
                await browser.switchToWindow(parentWindow.toString());
                console.log('Successfully switched to parent window:', parentWindow);
            } catch (error) {
                console.error('Error switching to parent window:', error);
            }

            if ((await url).includes('secure')) {

            } else {
                await elementActions.assertion(sensaRegistrationPageObject.lblsignIn_sensa);
            }
            console.log('Validated footerlinks in each page Successfully');
        } catch (error) {
            logger.error('Failed to Validat footerlinks in each page', { error });
            throw error;
        }
    }

    async siterequirementPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrSiteRequirement = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrSiteRequirement');
            const lbloptimal = testData.getCellValue(SHEET_NAME, scenarioname, 'lbloptimal');
            const hdrMobileReq = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrMobileReq');
            const lblBrowsers = testData.getCellValue(SHEET_NAME, scenarioname, 'lblBrowsers');
            const lblBrowserName = testData.getCellValue(SHEET_NAME, scenarioname, 'lblBrowserName');
            const hdrOS = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrOS');
            const lblOSNames = testData.getCellValue(SHEET_NAME, scenarioname, 'lblOSNames');
            const hdrDesktopReq = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrDesktopReq');
            const lbldesktopbrowser = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldesktopbrowser');
            const lbldesktopBName = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldesktopBName');
            const hdrDesktopOS = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrDesktopOS');
            const lblDesktopOSName = testData.getCellValue(SHEET_NAME, scenarioname, 'lblDesktopOSName');
            const hdrImportant = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrImportant');
            const lblimportantdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblimportantdesc');
            const hdrMobileandDesktop = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrMobileandDesktop');
            const lblJavascript = testData.getCellValue(SHEET_NAME, scenarioname, 'lblJavascript');
            const lblJavaScriptdesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lblJavaScriptdesc');
            const hdrCookies = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrCookies');
            const lblcookiesdescr = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcookiesdescr');
            const url = await browser.getUrl();
            if ((await url).includes('camel')) {
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrsitereq_camel, hdrSiteRequirement);
            } else if (url.includes('americanspirit')) {
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.hdrsitereq_nas, hdrSiteRequirement);
            } else {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrsitereq_sensa, hdrSiteRequirement);
            }
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbloptimal_sensa, lbloptimal);
            if ((await url).includes('camel')) {
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrmobilereq_camel, hdrMobileReq);
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblmobilebrowser_camel, lblBrowsers);
            } else if (url.includes('americanspirit')) {
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.hdrmobilereq_nas, hdrMobileReq);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblmobilebrowser_nas, lblBrowsers);
                await elementActions.assertion(NasFooterlinkPageObject.lblmobilebrowserdes_nas);
            } else {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrmobilereq_sensa, hdrMobileReq);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblmobilebrowser_sensa, lblBrowsers);
            }
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblmobilebrowserName_sensa, lblBrowserName);
            if ((await url).includes('camel')) {
                if ((await url).includes('secure')) {
                    await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbldesktopbrowser_camel, hdrOS);
                } else {
                    await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblmobileos_camel, hdrOS);
                }
            } else if (url.includes('americanspirit')) {
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblmobileos_nas, hdrOS);
                await elementActions.assertion(NasFooterlinkPageObject.lblmobileosdes_nas);
            } else {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblmobileos_sensa, hdrOS);
            }
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblmobileOSName_sensa, lblOSNames);
            if ((await url).includes('camel')) {
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrdesktopreq_camel, hdrDesktopReq);
                if ((await url).includes('secure')) {
                    await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblpostlogindesktopbrowser_camel, lbldesktopbrowser);
                } else {
                    await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbldesktopbrowser_camel, lbldesktopbrowser);
                }
            } else if (url.includes('americanspirit')) {
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.hdrdesktopreq_nas, hdrDesktopReq);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lbldesktopbrowser_nas, lbldesktopbrowser);
                await elementActions.assertion(NasFooterlinkPageObject.lbldesktopbrowserdes_nas)
            } else {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrdesktopreq_sensa, hdrDesktopReq);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbldesktopbrowser_sensa, lbldesktopbrowser);
            }
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbldesktopbrowserName_sensa, lbldesktopBName);
            if ((await url).includes('camel')) {
                if ((await url).includes('secure')) {
                    await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrcookies_camel, hdrDesktopOS);
                } else {
                    await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbldesktopOS_camel, hdrDesktopOS);
                }
            } else if (url.includes('americanspirit')) {
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lbldesktopOS_nas, hdrDesktopOS);
            } else {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbldesktopOS_sensa, hdrDesktopOS);
            }
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbldesktopOSName_sensa, lblDesktopOSName);
            if ((await url).includes('camel')) {
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrimportant_camel, hdrImportant);
                if (!(await url).includes('secure')) {
                    await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblimportantdesc_camel, lblimportantdesc);
                }
            } else if (url.includes('americanspirit')) {
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblimportantdesc_nas, lblimportantdesc);
            } else {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrimportant_sensa, hdrImportant);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblimportantdesc_sensa, lblimportantdesc);
            }

            if ((await url).includes('camel')) {
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdreqdesktopandmobile_camel, hdrMobileandDesktop);
                if ((await url).includes('secure')) {
                    await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrjavascriptpostlogin_camel, lblJavascript);
                    await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbljavscriptdescpostlogin_camel, lblJavaScriptdesc);
                } else {
                    await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrjavascript_camel, lblJavascript);
                    await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbljavscriptdesc_camel, lblJavaScriptdesc);
                }

            } else if (url.includes('americanspirit')) {
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.hdreqdesktopandmobile_nas, hdrMobileandDesktop);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.hdrjavascript_nas, lblJavascript);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lbljavscriptdesc_nas, lblJavaScriptdesc);
            } else {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdreqdesktopandmobile_sensa, hdrMobileandDesktop);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrjavascript_sensa, lblJavascript);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbljavscriptdesc_sensa, lblJavaScriptdesc);
            }

            if ((await url).includes('camel')) {
                if ((await url).includes('secure')) {
                    await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrcookiespostlogin_camel, hdrCookies);
                    await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblcookiesdespostlogin_camel, lblcookiesdescr);
                } else {
                    await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrcookies_camel, hdrCookies);
                    await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblcookiesdesc_camel, lblcookiesdescr);
                }

            } else if (url.includes('americanspirit')) {
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.hdrcookies_nas, hdrCookies);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblcookiesdesc_nas, lblcookiesdescr);
            } else {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrcookies_sensa, hdrCookies);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblcookiesdesc_sensa, lblcookiesdescr);
            }

            await this.footerlinkoneachpage();
            console.log('Validated the Content in  Site Requirement page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the content in  Site Requirement page', { error });
            throw error;
        }
    }

    async clickonsiterquirementprefooterlink() {
        try {
            await elementActions.clickusingJavascript(sensaLoginPageObject.lnksiteRequirement_sensa);
            await this.allowpopup();
            await this.windowswitchandback();
            const url = await browser.getUrl();
            if ((await url).includes('camel')) {
                await elementActions.assertion(camelFooterlinkPageObject.hdrsitereq_camel);
            } else if ((await url).includes('americanspirit')) {
                await elementActions.assertion(NasFooterlinkPageObject.hdrsitereq_nas);
            } else {
                await elementActions.assertion(sensaFooterlinkPageObject.hdrsitereq_sensa);
            }
            console.log('Navigated to Site Requirement Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Site Requirement Page', { error });
            throw error;
        }
    }

    async clickonsiterquirementpostfooterlink() {
        try {
            const url = await browser.getUrl();
            if (url.includes('camel')) {
                await elementActions.waitForDisplayed(camelFooterlinkPageObject.lnkSiteRequirementsPostLogin_camel);
                await elementActions.click(camelFooterlinkPageObject.lnkSiteRequirementsPostLogin_camel);
            } else {
                await elementActions.clickusingJavascript(sensaHomepagePageObject.lnksiterrequirements_sensa);
            }
            await this.allowpopup();
            await this.windowswitchandback();
            if (url.includes('camel')) {
                await elementActions.assertion(camelFooterlinkPageObject.hdrsitereq_camel);
            } else {
                await elementActions.assertion(sensaFooterlinkPageObject.hdrsitereq_sensa);
            }
            console.log('Navigated to Site Requirement Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Site Requirement Page', { error });
            throw error;
        }
    }

    async clickontermsOfUseprefooterlink() {
        try {
            await elementActions.clickusingJavascript(sensaLoginPageObject.lnktermsofUse_sensa);
            await this.allowpopup();
            await this.windowswitchandback();
            await elementActions.assertion(sensaFooterlinkPageObject.hdrtermsOfUse_sensa);
            console.log('Navigated to Terms Of Use Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Terms Of Use Page', { error });
            throw error;
        }
    }

    async clickontermsOfUsepostfooterlink() {
        try {
            const url = await browser.getUrl();
            if (url.includes('camel')) {
                await elementActions.click(camelFooterlinkPageObject.lnktermsofUsepostlogin_sensa);
            } else {
                await elementActions.clickusingJavascript(sensaHomepagePageObject.lnktermsofuse_sensa);
            }
            await this.allowpopup();
            await this.windowswitchandback();
            await elementActions.assertion(sensaFooterlinkPageObject.hdrtermsOfUse_sensa);
            console.log('Navigated to Terms Of Use Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Terms Of Use Page', { error });
            throw error;
        }
    }


    async privacyPolicyPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrprivacyPolicy = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrprivacyPolicy');
            const lnkrestrictionsonaccess = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkrestrictionsonaccess');
            const lnkwhywecollect = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkwhywecollect');
            const lnkwhatwecollect = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkwhatwecollect');
            const lnkwwemayshare = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkwwemayshare');
            const lnkhowweprotect = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkhowweprotect');
            const lnkhowcanyoumanage = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkhowcanyoumanage');
            const lnklinkstothird = testData.getCellValue(SHEET_NAME, scenarioname, 'lnklinkstothird');
            const lnkdataretension = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkdataretension');
            const lnknoticetocalifornia = testData.getCellValue(SHEET_NAME, scenarioname, 'lnknoticetocalifornia');
            const lnkchangesto = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkchangesto');
            const lnkcontact = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkcontact');
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrprivacyPolicy_sensa, hdrprivacyPolicy);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkrestrictionsonaccess_sensa, lnkrestrictionsonaccess);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkwhywecollect_sensa, lnkwhywecollect);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkwhatwecollect_sensa, lnkwhatwecollect);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkwwemayshare_sensa, lnkwwemayshare);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkhowweprotect_sensa, lnkhowweprotect);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkhowcanyoumanage_sensa, lnkhowcanyoumanage);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnklinkstothird_sensa, lnklinkstothird);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkdataretension_sensa, lnkdataretension);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnknoticetocalifornia_sensa, lnknoticetocalifornia);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkchangesto_sensa, lnkchangesto);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkcontact_sensa, lnkcontact);
            const lblprivacypolicy = testData.getCellValue(SHEET_NAME, scenarioname, 'lblprivacypolicy');
            const lblrestrictionsonaccess = testData.getCellValue(SHEET_NAME, scenarioname, 'lblrestrictionsonaccess');
            const lblwhywecollect = testData.getCellValue(SHEET_NAME, scenarioname, 'lblwhywecollect');
            const lblwhatwecollect = testData.getCellValue(SHEET_NAME, scenarioname, 'lblwhatwecollect');
            const lblwwemayshare = testData.getCellValue(SHEET_NAME, scenarioname, 'lblwwemayshare');
            const lblhowweprotect = testData.getCellValue(SHEET_NAME, scenarioname, 'lblhowweprotect');
            const lblhowcanyoumanage = testData.getCellValue(SHEET_NAME, scenarioname, 'lblhowcanyoumanage');
            const lbllinkstothird = testData.getCellValue(SHEET_NAME, scenarioname, 'lbllinkstothird');
            const lbldataretension = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldataretension');
            const lbldataretensiondesc = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldataretensiondesc');
            const lblnoticetocalifornia = testData.getCellValue(SHEET_NAME, scenarioname, 'lblnoticetocalifornia');
            const lblchangesto = testData.getCellValue(SHEET_NAME, scenarioname, 'lblchangesto');
            const lblcontact = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcontact');
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblprivacypolicy_sensa, lblprivacypolicy);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblrestrictionson_sensa, lblrestrictionsonaccess);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwhywecollect_sensa, lblwhywecollect);
            const url = await browser.getUrl();
            if (url.includes('camel')) {
                if (url.includes('aem')) {
                    await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwhatwecollect_sensa, lblwhatwecollect);
                }
            } else {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwhatwecollect_sensa, lblwhatwecollect);
            }
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwwemayshare_sensa, lblwwemayshare);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblhowweprotect_sensa, lblhowweprotect);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblhowcanyoumanage_sensa, lblhowcanyoumanage);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbllinkstothird_sensa, lbllinkstothird);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbldataretension_sensa, lbldataretension);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbldataretensiondesc_sensa, lbldataretensiondesc);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblnoticetocalifornia_sensa, lblnoticetocalifornia);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblchangesto_sensa, lblchangesto);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblcontact_sensa, lblcontact);
            await this.footerlinkoneachpage();
            console.log('Validated the Content in Privacy Policy page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the content in Privacy Policy page', { error });
            throw error;
        }
    }

    async clickonprivacyPolicyprefooterlink() {
        try {
            const url = await browser.getUrl();
            if (url.includes('camel')) {
                await elementActions.click(camelLoginPageObject.lnkprivacyPolicy_camel);
            } else if (url.includes('americanspirit')) {
                await elementActions.click(NasFooterlinkPageObject.lnkprivacyPolicy_nas);
            } else if (url.includes('sensa')) {
                await elementActions.clickusingJavascript(sensaLoginPageObject.lnkprivacyPolicy_sensa);
            }
            await this.allowpopup();
            await this.windowswitchandback();
            await elementActions.assertion(sensaFooterlinkPageObject.hdrprivacyPolicy_sensa);
            console.log('Navigated to Privacy Policy Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Privacy Policy Page', { error });
            throw error;
        }
    }

    async clickonprivacyPolicypostfooterlink() {
        try {
            const url = await browser.getUrl();
            if (url.includes('camel')) {
                await elementActions.click(camelFooterlinkPageObject.lnkprivacypolicy_camel);
            } else {
                await elementActions.clickusingJavascript(sensaHomepagePageObject.lnkprivacypolicy_sensa);
            }
            await this.allowpopup();
            await this.windowswitchandback();
            await elementActions.assertion(sensaFooterlinkPageObject.hdrprivacyPolicy_sensa);
            console.log('Navigated to Privacy Policy Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Privacy Policy Page', { error });
            throw error;
        }
    }

    async termsofUsePageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrtermsOfUse = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrtermsOfUse');
            const lnkrestrictionson = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkrestrictionson');
            const lnkaccount = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkaccount');
            const lnkstuff = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkstuff');
            const lnkconduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkconduct');
            const lnkcontenton = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkcontenton');
            const lnkourPrivacy = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkourPrivacy');
            const lnkintellectual = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkintellectual');
            const lnksiteAvailable = testData.getCellValue(SHEET_NAME, scenarioname, 'lnksiteAvailable');
            const lnkchoiceof = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkchoiceof');
            const lnkdisclaimerof = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkdisclaimerof');
            const lnkindemnity = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkindemnity');
            const lnkiresolvingDispute = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkiresolvingDispute');
            const lnkspecialNotice = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkspecialNotice');
            const lnkelectronicSignature = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkelectronicSignature');
            const lnkmiscellaneous = testData.getCellValue(SHEET_NAME, scenarioname, 'lnkmiscellaneous');
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrtermsOfUse_sensa, hdrtermsOfUse);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkrestrictionson_sensa, lnkrestrictionson);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkaccount_sensa, lnkaccount);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkstuff_sensa, lnkstuff);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkconduct_sensa, lnkconduct);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkcontenton_sensa, lnkcontenton);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkourPrivacy_sensa, lnkourPrivacy);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkintellectual_sensa, lnkintellectual);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnksiteAvailable_sensa, lnksiteAvailable);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkchoiceof_sensa, lnkchoiceof);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkdisclaimerof_sensa, lnkdisclaimerof);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkindemnity_sensa, lnkindemnity);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkiresolvingDispute_sensa, lnkiresolvingDispute);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkspecialNotice_sensa, lnkspecialNotice);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkelectronicSignature_sensa, lnkelectronicSignature);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lnkmiscellaneous_sensa, lnkmiscellaneous);
            const lblthefollowingterms = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthefollowingterms');
            const lblaccessanduse = testData.getCellValue(SHEET_NAME, scenarioname, 'lblaccessanduse');
            const lblrestrictionson = testData.getCellValue(SHEET_NAME, scenarioname, 'lblrestrictionson');
            const lblaccount = testData.getCellValue(SHEET_NAME, scenarioname, 'lblaccount');
            const lblstuff = testData.getCellValue(SHEET_NAME, scenarioname, 'lblstuff');
            const lblconduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblconduct');
            const lblcontenton = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcontenton');
            const lblourPrivacy = testData.getCellValue(SHEET_NAME, scenarioname, 'lblourPrivacy');
            const lblintellectual = testData.getCellValue(SHEET_NAME, scenarioname, 'lblintellectual');
            const lblsiteAvailable = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsiteAvailable');
            const lblchoiceof = testData.getCellValue(SHEET_NAME, scenarioname, 'lblchoiceof');
            const lbldisclaimerof = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldisclaimerof');
            const lblindemnity = testData.getCellValue(SHEET_NAME, scenarioname, 'lblindemnity');
            const lbliresolvingDispute = testData.getCellValue(SHEET_NAME, scenarioname, 'lbliresolvingDispute');
            const lblspecialNotice = testData.getCellValue(SHEET_NAME, scenarioname, 'lblspecialNotice');
            const lblelectronicSignature = testData.getCellValue(SHEET_NAME, scenarioname, 'lblelectronicSignature');
            const lblmiscellaneous = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmiscellaneous');
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblthefollowingterms_sensa, lblthefollowingterms);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblaccessanduse_sensa, lblaccessanduse);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblrestrictionson_sensa, lblrestrictionson);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblaccount_sensa, lblaccount);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblstuff_sensa, lblstuff);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblconduct_sensa, lblconduct);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblcontenton_sensa, lblcontenton);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblourPrivacy_sensa, lblourPrivacy);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblintellectual_sensa, lblintellectual);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblsiteAvailable_sensa, lblsiteAvailable);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblchoiceof_sensa, lblchoiceof);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbldisclaimerof_sensa, lbldisclaimerof);
            const url = await browser.getUrl();
            if (url.includes('americanspirit')) {
                await elementActions.assertion(sensaFooterlinkPageObject.lblindemnity_sensa);
            } else {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblindemnity_sensa, lblindemnity);
            }
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbliresolvingDispute_sensa, lbliresolvingDispute);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblspecialNotice_sensa, lblspecialNotice);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblelectronicSignature_sensa, lblelectronicSignature);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblmiscellaneous_sensa, lblmiscellaneous);
            await this.footerlinkoneachpage();
            console.log('Validated the Content in  Terms of use page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the content in  Terms of use page', { error });
            throw error;
        }
    }

    async clickontextmessagingprefooterlink() {
        try {
            const url = await browser.getUrl();
            if ((await url).includes('camel')) {
                await elementActions.click(camelLoginPageObject.lnktextMessaging_camel);
            } else if ((await url).includes('americanspirit')) {
                await elementActions.click(NasFooterlinkPageObject.lnktextMessaging_nas);
            } else if ((await url).includes('sensa')) {
                await elementActions.clickusingJavascript(sensaLoginPageObject.lnktextMessaging_sensa);
            }
            await this.allowpopup();
            await this.windowswitchandback();
            if ((await url).includes('camel')) {
                await elementActions.assertion(camelFooterlinkPageObject.hdrtextMessaging_camel);
            } else if ((await url).includes('americanspirit')) {
                await elementActions.click(NasFooterlinkPageObject.hdrtextMessaging_nas);
            } else if ((await url).includes('sensa')) {
                await elementActions.assertion(sensaFooterlinkPageObject.hdrtextMessaging_sensa);
            }
            console.log('Navigated to Text Messaging Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Text Messaging Page', { error });
            throw error;
        }
    }

    async clickontextmessagingpostfooterlink() {
        try {
            const url = await browser.getUrl();
            if (url.includes('camel')) {
                await elementActions.click(camelFooterlinkPageObject.lnktextmessaging_camel);
            } else {
                await elementActions.clickusingJavascript(sensaHomepagePageObject.lnktextmessaging_sensa);
            }
            await this.allowpopup();
            await this.windowswitchandback();
            if (url.includes('camel')) {
                await elementActions.assertion(camelFooterlinkPageObject.hdrtextMessaging_camel);
            } else {
                await elementActions.assertion(sensaFooterlinkPageObject.hdrtextMessaging_sensa);
            }
            console.log('Navigated to Text Messaging Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Text Messaging Page', { error });
            throw error;
        }
    }

    async textMessagingPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrtextMessaging = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrtextMessaging');
            const lbleachOfRJR = testData.getCellValue(SHEET_NAME, scenarioname, 'lbleachOfRJR');
            const lblconsenttoReceipt = testData.getCellValue(SHEET_NAME, scenarioname, 'lblconsenttoReceipt');
            const lblmodificationofTerms = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmodificationofTerms');
            const lbluserOptIn = testData.getCellValue(SHEET_NAME, scenarioname, 'lbluserOptIn');
            const lbluserOptOut = testData.getCellValue(SHEET_NAME, scenarioname, 'lbluserOptOut');
            const lbldutytoNotify = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldutytoNotify');
            const lblmobileandAT = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmobileandAT');
            const lblyouAgree = testData.getCellValue(SHEET_NAME, scenarioname, 'lblyouAgree');
            const lblprogramDescription = testData.getCellValue(SHEET_NAME, scenarioname, 'lblprogramDescription');
            const lblcostandFrequency = testData.getCellValue(SHEET_NAME, scenarioname, 'lblcostandFrequency');
            const lblsupportInstructions = testData.getCellValue(SHEET_NAME, scenarioname, 'lblsupportInstructions');
            const lblmmsDisclosure = testData.getCellValue(SHEET_NAME, scenarioname, 'lblmmsDisclosure');
            const lblourDisclaimerof = testData.getCellValue(SHEET_NAME, scenarioname, 'lblourDisclaimerof');
            const lbladultTobacco = testData.getCellValue(SHEET_NAME, scenarioname, 'lbladultTobacco');
            const lbltruthfuland = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltruthfuland');
            const lbldisputeResolution = testData.getCellValue(SHEET_NAME, scenarioname, 'lbldisputeResolution');
            const lblthepartiesagree = testData.getCellValue(SHEET_NAME, scenarioname, 'lblthepartiesagree');
            const lblMiscellaneous = testData.getCellValue(SHEET_NAME, scenarioname, 'lblMiscellaneous');
            const url = await browser.getUrl();
            if ((await url).includes('camel')) {
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrtextMessaging_camel, hdrtextMessaging);
            } else if ((await url).includes('americanspirit')) {
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.hdrtextMessaging_nas, hdrtextMessaging);
            } else {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrtextMessaging_sensa, hdrtextMessaging);
            }

            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbleachOfRJR_sensa, lbleachOfRJR);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblconsenttoReceipt_sensa, lblconsenttoReceipt);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblmodificationofTerms_sensa, lblmodificationofTerms);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbluserOptIn_sensa, lbluserOptIn);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbluserOptOut_sensa, lbluserOptOut);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbldutytoNotify_sensa, lbldutytoNotify);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblmobileandAT_sensa, lblmobileandAT);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblyouAgree_sensa, lblyouAgree);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblprogramDescription_sensa, lblprogramDescription);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblcostandFrequency_sensa, lblcostandFrequency);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblsupportInstructions_sensa, lblsupportInstructions);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblmmsDisclosure_sensa, lblmmsDisclosure);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblourDisclaimerof_sensa, lblourDisclaimerof);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbladultTobacco_sensa, lbladultTobacco);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbltruthfuland_sensa, lbltruthfuland);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lbldisputeResolution_sensa, lbldisputeResolution);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblthepartiesagree_sensa, lblthepartiesagree);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblMiscellaneous_sensa, lblMiscellaneous);
            await this.footerlinkoneachpage();
            console.log('Validated the Content in Text Messaging page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the content in Text Messaging page', { error });
            throw error;
        }
    }

    async clickoncontactuspostfooterlink() {
        try {
            const url = await browser.getUrl();
            if (url.includes('sensa')) {
                await elementActions.click(sensaHomepagePageObject.lnkcontactus_sensa);
            } else {
                await elementActions.clickusingJavascript(camelFooterlinkPageObject.lnkcontactUsPostLogin_camel);
            }
            await this.allowpopup();
            await this.windowswitchandback();
            if (url.includes('sensa')) {
                await elementActions.assertion(sensaFooterlinkPageObject.hdrpostloginContactus_sensa);
            } else {
                await elementActions.assertion(camelFooterlinkPageObject.hdrpostloginContactus_camel);
            }
            console.log('Navigated to Contact us Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Contact Us Page', { error });
            throw error;
        }
    }
    async contactuspostloginPageValidation(_filename: string, _sheetname: string, _scenarioname: string) {
        try {

            const SHEET_NAME = _sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', _filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, _scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrpostloginContactus = testData.getCellValue(SHEET_NAME, _scenarioname, 'hdrpostloginContactus');
            const lblourHours = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblourHours');
            const lblmondayToSaturday = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblmondayToSaturday');
            const lblsendAQuestion = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblsendAQuestion');
            const hdrchat = testData.getCellValue(SHEET_NAME, _scenarioname, 'hdrchat');
            const hdremail = testData.getCellValue(SHEET_NAME, _scenarioname, 'hdremail');
            const hdrcall = testData.getCellValue(SHEET_NAME, _scenarioname, 'hdrcall');
            const btncall = testData.getCellValue(SHEET_NAME, _scenarioname, 'btncall');
            const lblmessage = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblmessage');
            const txtdropdown = testData.getCellValue(SHEET_NAME, _scenarioname, 'txtdropdown');
            const txtmessage = testData.getCellValue(SHEET_NAME, _scenarioname, 'txtmessage');
            const url = await browser.getUrl();
            if (url.includes('sensa')) {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrpostloginContactus_sensa, hdrpostloginContactus);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblourHours_sensa, lblourHours);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblmondayToSaturday_sensa, lblmondayToSaturday);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblsendAQuestion_sensa, lblsendAQuestion);
            } else {
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrpostloginContactus_camel, hdrpostloginContactus);
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhours_camel, lblourHours);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblourHours_sensa, lblmondayToSaturday);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblmondayToSaturday_sensa, lblsendAQuestion);
            }

            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrchat_sensa, hdrchat);
            await elementActions.assertion(sensaFooterlinkPageObject.btnchat_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdremail_sensa, hdremail);
            await elementActions.assertion(sensaFooterlinkPageObject.txtdropdown_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblmessage_sensa, lblmessage);
            await elementActions.assertion(sensaFooterlinkPageObject.txtmessage_sensa);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrcall_sensa, hdrcall);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.btncall_sensa, btncall);
            await elementActions.assertion(sensaFooterlinkPageObject.btnsubmit_sensa);
            const dropdown = await sensaFooterlinkPageObject.txtdropdown_sensa;
            const isDisplayed = await dropdown.isDisplayed();
            if (isDisplayed) {
                const options = await dropdown.$$('option');
                for (let j = 0; j < await options.length; j++) {
                    await dropdown.selectByIndex(j);
                }

            }
            (await sensaFooterlinkPageObject.txtdropdown_sensa).selectByVisibleText(txtdropdown);
            await elementActions.setValue(sensaFooterlinkPageObject.txtmessage_sensa, txtmessage);
            await assertionHelper.assertElementEnabled(sensaFooterlinkPageObject.btnsubmit_sensa);
            await this.footerlinkoneachpage();
            console.log('Validated the Content in  Contact Us page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the content in  Contact Us page', { error });
            throw error;
        }
    }

    async clickonfaqpostfooterlink() {
        try {
            const url = await browser.getUrl();
            if (url.includes('americanspirit')) {
                await elementActions.clickusingJavascript(NasFooterlinkPageObject.lnkpostfaq_nas);
            } else {
                await elementActions.clickusingJavascript(sensaHomepagePageObject.lnkfaq_sensa);
                await this.allowpopup();
                await this.windowswitchandback();
            }
            await elementActions.assertion(sensaFooterlinkPageObject.hdrfaq_ContactUs_sensa);
            console.log('Navigated to FAQ Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to FAQ Page', { error });
            throw error;
        }
    }

    async clickontobaccorightsprefooterlink() {
        try {
            const url = await browser.getUrl();
            if (url.includes('camel')) {
                if (url.includes('aem')) {
                    await elementActions.clickusingJavascript(camelLoginPageObject.lnktobacco_camel);
                } else {
                    await elementActions.clickusingJavascript(camelLoginPageObject.lnktobaccopostlogin_camel);
                }
            } else if (url.includes('americanspirit')) {
                await elementActions.clickusingJavascript(camelLoginPageObject.lnktobacco_camel);
            }
            await this.allowpopup();
            await this.windowswitchandback();
            await elementActions.clickElementIfExistsAndVisible(camelFooterlinkPageObject.btnacceptCookies_camel);
            await elementActions.assertion(camelFooterlinkPageObject.hdrtobaccoRights_camel);

            console.log('Navigated to Tobacco Rights Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to  Tobacco Rights Page', { error });
            throw error;
        }
    }

    async clickonTheSpiritCirclefooterlink() {
        try {
            const url = await browser.getUrl();
            await elementActions.clickusingJavascript(NasFooterlinkPageObject.hdrSpiritCircleTerms_nas);
            await this.allowpopup();
            await this.windowswitchandback();
            if ((await url).includes('aem')) {
                await elementActions.assertion(NasFooterlinkPageObject.hdrSpiritCircleProgramTerms_nas);
            } else {
                await elementActions.assertion(NasFooterlinkPageObject.prodhdrSpiritCircleProgramTerms_nas);
            }
            console.log('Navigated to The Spirit Circle Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to  The Spirit Circle Page', { error });
            throw error;
        }
    }

    async tobaccoRightspreloginPageValidation(_filename: string, _sheetname: string, _scenarioname: string) {
        try {

            const SHEET_NAME = _sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', _filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, _scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrtobaccoRights = testData.getCellValue(SHEET_NAME, _scenarioname, 'hdrtobaccoRights');
            const lblownit = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblownit');
            const lblspeakup = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblspeakup');
            const lblproposals = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblproposals');
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrtobaccoRights_camel, hdrtobaccoRights);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblownit_camel, lblownit);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblspeakup_camel, lblspeakup);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblproposals_camel, lblproposals);
            await elementActions.assertion(camelFooterlinkPageObject.btntakeactionNow_camel);
            await elementActions.assertion(camelFooterlinkPageObject.icnYoutube_camel);
            await elementActions.assertion(camelFooterlinkPageObject.icnInsta_camel);
            await elementActions.assertion(camelFooterlinkPageObject.icnFacebook_camel);
            await elementActions.assertion(camelFooterlinkPageObject.icnTwitter_camel);
            await browser.execute(() => { window.close(); });
            await this.windowswitchandback();
            const url = await browser.getUrl();
            if ((await url).includes('secure')) {
                await camelLoginPage.camellogoutsucessfully();
            }
            console.log('Validated the Content in  Tobacco Rights page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the content in  Tobacco Rights page', { error });
            throw error;
        }
    }

    async faqPageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const contactus = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrcontactUs');
            const lblfurtherassistance = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfurtherassistance');
            const lblfurtherquestion = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfurtherquestion');
            const lblonthispage = testData.getCellValue(SHEET_NAME, scenarioname, 'lblonthispage');
            const url = await browser.getUrl();
            if (url.includes('secure')) {
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrfaq_ContactUs_sensa, contactus);
            } else {
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_ContactUs_camel, contactus);
            }

            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblfurtherassistance, lblfurtherassistance);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblfrequentQuestions, lblfurtherquestion);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblonthisPage_sensa, lblonthispage);
            const hdrlogin = testData.getCellValue(SHEET_NAME, scenarioname, 'lblwarnings');
            const lblQuestion1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion1');
            const lblQuestion2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion2');
            const lblQuestion3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion3');
            const lblQuestion4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion4');
            const lblQuestion5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion5');
            const hdroffers = testData.getCellValue(SHEET_NAME, scenarioname, 'lbloffersandpromotions');
            const lblQuestion6 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion6');
            const lblQuestion7 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion7');
            const lblQuestion8 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion8');
            const lblQuestion9 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion9');
            const lblQuestion10 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion10');
            const hdrageverification = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAge');
            const lblQuestion11 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion11');
            const lblQuestion12 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion12');
            const hdrprivacy = testData.getCellValue(SHEET_NAME, scenarioname, 'lblprivacy');
            const lblQuestion13 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion13');
            const lblQuestion14 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion14');
            const hdrtroubleshooting = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltroubleshooting');
            const lblQuestion15 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion15');
            const lblQuestion16 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion16');
            const lblQuestion17 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion17');
            const hdrgeneral = testData.getCellValue(SHEET_NAME, scenarioname, 'lblgeneral');
            const lblQuestion18 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion18');
            const hdrenvironment = testData.getCellValue(SHEET_NAME, scenarioname, 'lblenvironment');
            const lblQuestion19 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion19');
            const lblQuestion20 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion20');
            const lblQuestion21 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion21');
            const lblQuestion22 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion22');
            const lblQuestion23 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion23');
            const lblQuestion24 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion24');

            const lblAnswer1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer1');
            const lblAnswer2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer2');
            const lblAnswer3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer3');
            const lblAnswer4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer4');
            const lblAnswer5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer5');
            const lblAnswer6 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer6');
            const lblAnswer7 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer7');
            const lblAnswer8_1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer81');
            const lblAnswer8_2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer82');
            const lblAnswer9 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer9');
            const lblAnswer10 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer10');
            const lblAnswer11 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer11');
            const lblAnswer12 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer12');
            const lblAnswer13 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer13');
            const lblAnswer14 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer14');
            const lblAnswer151 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer151');
            const lblAnswer152 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer152');
            const lblAnswer16 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer16');
            const lblAnswer171 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer171');
            const lblAnswer172 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer172');
            const lblAnswer18 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer18');
            const lblAnswer191 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer191');
            const lblAnswer192 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer192');
            const lblAnswer193 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer193');
            const lblAnswer20 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer20');
            const lblAnswer211 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer211');
            const lblAnswer212 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer212');
            const lblAnswer221 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer221');
            const lblAnswer222 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer222');
            const lblAnswer231 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer231');
            const lblAnswer232 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer232');
            const lblAnswer24 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer24');

            const lblneedfurther = testData.getCellValue(SHEET_NAME, scenarioname, 'lblneedfurther');
            const lblcontactprod = testData.getCellValue(SHEET_NAME, scenarioname, 'lblourcontact_prod');

            if (!url.includes('secure')) {
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_loginandpassword_camel, hdrlogin);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ1_sensa, lblQuestion1);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ1_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA1_sensa, lblAnswer1);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ2_sensa, lblQuestion2);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ2_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA2_sensa, lblAnswer2);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ3_sensa, lblQuestion3);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ3_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA3_sensa, lblAnswer3);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ4_sensa, lblQuestion4);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ4_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA4_sensa, lblAnswer4);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ5_sensa, lblQuestion5);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ5_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA5_sensa, lblAnswer5);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_offersandpromotions_camel, hdroffers);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ6_sensa, lblQuestion6);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ6_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA6_sensa, lblAnswer6);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ7_sensa, lblQuestion7);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ7_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA7_sensa, lblAnswer7);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ8_sensa, lblQuestion8);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ8_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA8_sensa, lblAnswer8_1);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA82_sensa, lblAnswer8_2);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ9_sensa, lblQuestion9);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ9_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA92_sensa, lblAnswer9);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ10_sensa, lblQuestion10);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ10_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA93_sensa, lblAnswer10);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_ageverification_camel, hdrageverification);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ11_sensa, lblQuestion11);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ11_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA94_sensa, lblAnswer11);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblProductQ1_sensa, lblQuestion12);
                await elementActions.click(sensaFooterlinkPageObject.lblProductQ1_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA95_sensa, lblAnswer12);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_privacy_camel, hdrprivacy);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ2_sensa, lblQuestion13);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ2_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA96_sensa, lblAnswer13);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ3_sensa, lblQuestion14);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ3_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA10_1_sensa, lblAnswer14);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_troubleshoot_camel, hdrtroubleshooting);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ4_sensa, lblQuestion15);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ4_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA10_2_sensa, lblAnswer151);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA11_2_sensa, lblAnswer152);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ5_sensa, lblQuestion16);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ5_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA10_4_sensa, lblAnswer16);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ6_sensa, lblQuestion17);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ6_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA11_sensa, lblAnswer171);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA11_3_sensa, lblAnswer172);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_general_camel, hdrgeneral);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ7_sensa, lblQuestion18);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ7_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA2_sensa, lblAnswer18);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_environment_camel, hdrenvironment);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ8_sensa, lblQuestion19);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ8_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA3_sensa, lblAnswer191);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA5_sensa, lblAnswer192);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA7_sensa, lblAnswer193);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ9_sensa, lblQuestion20);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ9_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA8_sensa, lblAnswer20);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ10_sensa, lblQuestion21);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ10_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA9_sensa, lblAnswer211);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA11_sensa, lblAnswer212);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ11_sensa, lblQuestion22);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ11_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA12_sensa, lblAnswer221);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA14_sensa, lblAnswer222);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ12_sensa, lblQuestion23);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ12_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA15_sensa, lblAnswer231);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA17_sensa, lblAnswer232);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ13_sensa, lblQuestion24);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ13_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA18_sensa, lblAnswer24);
            } else {
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_offersandpromotions_camel, hdroffers);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ1_sensa, lblQuestion6);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ1_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA1_sensa, lblAnswer6);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ2_sensa, lblQuestion7);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ2_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA2_sensa, lblAnswer7);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ3_sensa, lblQuestion8);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ3_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA3_sensa, lblAnswer8_1);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA82_sensa, lblAnswer8_2);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ4_sensa, lblQuestion9);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ4_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA5_sensa, lblAnswer9);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ5_sensa, lblQuestion10);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ5_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA6_sensa, lblAnswer10);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_ageverification_camel, hdrageverification);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ6_sensa, lblQuestion11);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ6_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA7_sensa, lblAnswer11);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ7_sensa, lblQuestion12);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ7_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA8_sensa, lblAnswer12);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_privacy_camel, hdrprivacy);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ8_sensa, lblQuestion13);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ8_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA91_sensa, lblAnswer13);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ9_sensa, lblQuestion14);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ9_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA92_sensa, lblAnswer14);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_troubleshoot_camel, hdrtroubleshooting);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ10_sensa, lblQuestion15);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ10_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA93_sensa, lblAnswer151);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA11_2_sensa, lblAnswer152);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningQ11_sensa, lblQuestion16);
                await elementActions.click(sensaFooterlinkPageObject.lblwarningQ11_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA95_sensa, lblAnswer16);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblProductQ1_sensa, lblQuestion17);
                await elementActions.click(sensaFooterlinkPageObject.lblProductQ1_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA96_sensa, lblAnswer171);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA11_3_sensa, lblAnswer172);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_general_camel, hdrgeneral);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ2_sensa, lblQuestion18);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ2_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA10_2_sensa, lblAnswer18);

                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.hdrfaq_environment_camel, hdrenvironment);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ4_sensa, lblQuestion19);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ4_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblwarningA10_4_sensa, lblAnswer191);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA1_sensa, lblAnswer192);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA3_sensa, lblAnswer193);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ5_sensa, lblQuestion20);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ5_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA4_sensa, lblAnswer20);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ6_sensa, lblQuestion21);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ6_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA5_sensa, lblAnswer211);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA7_sensa, lblAnswer212);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ7_sensa, lblQuestion22);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ7_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA8_sensa, lblAnswer221);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA10_sensa, lblAnswer222);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ8_sensa, lblQuestion23);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ8_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA11_sensa, lblAnswer231);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA13_sensa, lblAnswer232);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductQ9_sensa, lblQuestion24);
                await elementActions.click(sensaFooterlinkPageObject.lblproductQ9_sensa);
                await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblproductA14_sensa, lblAnswer24);

            }
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.hdrneedfurther_sensa, lblneedfurther);
            await sensaAccountPage.mssgcomparision(sensaFooterlinkPageObject.lblourcontact_sensa, lblcontactprod);
            await this.footerlinkoneachpage();
            console.log('Validated the Content in FAQ page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in FAQ page', { error });
            throw error;
        }
    }


    async clickoncamelpointsprefooterlink() {
        try {
            await elementActions.clickusingJavascript(camelLoginPageObject.lnkcamelpoits_camel);
            await this.allowpopup();
            await this.windowswitchandback();
            await elementActions.assertion(camelFooterlinkPageObject.hdrcamelPoints_camel);

            console.log('Navigated to Camel Points Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to  Camel Points Page', { error });
            throw error;
        }
    }

    async NasTheSpiritCirclepageValidation(_filename: string, _sheetname: string, _scenarioname: string) {
        try {

            const SHEET_NAME = _sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', _filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, _scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrcamelPoints = testData.getCellValue(SHEET_NAME, _scenarioname, 'hdrcamelPoints');
            const lblcamelPointsterms = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblcamelPointsterms');
            const lblcamelPointsparara1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblcamelPointsparara1');
            const lblcamelPointsparara2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblcamelPointsparara2');
            await sensaAccountPage.mssgcomparisionremovelinespace(camelFooterlinkPageObject.hdrcamelPoints_camel, hdrcamelPoints);

            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblcamelPointsparara1_camel, lblcamelPointsparara1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblcamelPointsparara2_camel, lblcamelPointsparara2);

            const lbleligibility = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbleligibility');
            const lbljoiningpgr = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbljoiningpgr');
            const lblhowewecommunicate = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowewecommunicate');
            const lblhowtoearn = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearn');
            const lblhowtoredeem = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoredeem');
            const lblhowtoavoid = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoavoid');
            const lblavailablerwards = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblavailablerwards');
            const lblreserved = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblreserved');
            const lblprogram = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogram');
            const lbladditional = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbladditional');
            const lblclosing = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblclosing');
            const lbldisclaimer = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbldisclaimer');
            const lblchanges = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblchanges');
            const lblprivacy = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacy');
            const lblindemnity = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblindemnity');
            const lblchoiceoflaw = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblchoiceoflaw');
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbleligibility_camel, lbleligibility);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbljoiningpgr_camel, lbljoiningpgr);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowewecommunicate_camel, lblhowewecommunicate);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearn_camel, lblhowtoearn);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoredeem_camel, lblhowtoredeem);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoavoid_camel, lblhowtoavoid);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblavailablerwards_camel, lblavailablerwards);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblreserved_camel, lblreserved);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogram_camel, lblprogram);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbladditional_camel, lbladditional);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblclosing_camel, lblclosing);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbldisclaimer_camel, lbldisclaimer);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblchanges_camel, lblchanges);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacy_camel, lblprivacy);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblindemnity_camel, lblindemnity);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblchoiceoflaw_camel, lblchoiceoflaw);

            const lbleligibilitydesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbleligibilitydesc1');
            await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lbleligibilitydesc1_nas, lbleligibilitydesc1);
            await elementActions.assertion(NasFooterlinkPageObject.lbleligibilitydesc2_nas);
            await elementActions.assertion(NasFooterlinkPageObject.lblhowewecommunicatedesc1_nas);
            await elementActions.assertion(NasFooterlinkPageObject.lbljoiningpgrdesc1_nas);
            await elementActions.assertion(NasFooterlinkPageObject.lblhowtoearndesc1_nas);
            await elementActions.assertion(NasFooterlinkPageObject.lblhowtoredeemdesc1_nas);
            await elementActions.assertion(NasFooterlinkPageObject.lblhowtoavoiddesc1_nas);
            await elementActions.assertion(NasFooterlinkPageObject.lblavailablerwardsdesc1_nas);
            await elementActions.assertion(NasFooterlinkPageObject.lblreserveddesc1_nas);
            await elementActions.assertion(NasFooterlinkPageObject.lblprogramdesc1_nas);
            await elementActions.assertion(NasFooterlinkPageObject.lbladditionaldesc1_nas);
            await elementActions.assertion(NasFooterlinkPageObject.lblclosingdesc1_nas);
            await elementActions.assertion(NasFooterlinkPageObject.lbldisclaimerdesc1_nas);
            await elementActions.assertion(NasFooterlinkPageObject.lblchangesdesc1_nas);
            await elementActions.assertion(NasFooterlinkPageObject.lblindemnitydesc1_nas);
            await elementActions.assertion(NasFooterlinkPageObject.lblchoiceoflawdesc1_nas);

            await this.footerlinkoneachpage();
            console.log('Validated the Content in The Spirit Circle page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the content in The Spirit Circle page', { error });
            throw error;
        }
    }

    async camelPOintsPageValidation(_filename: string, _sheetname: string, _scenarioname: string) {
        try {

            const SHEET_NAME = _sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', _filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, _scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const hdrcamelPoints = testData.getCellValue(SHEET_NAME, _scenarioname, 'hdrcamelPoints');
            const lblcamelPointsterms = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblcamelPointsterms');
            const lblcamelPointsparara1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblcamelPointsparara1');
            const lblcamelPointsparara2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblcamelPointsparara2');
            const url = await browser.getUrl();
            if ((await url).includes('camel')) {
                await sensaAccountPage.mssgcomparisionremovelinespace(camelFooterlinkPageObject.hdrcamelPoints_camel, hdrcamelPoints);
                await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblcamelPointsterms_camel, lblcamelPointsterms);
            } else if ((await url).includes('americanspirit')) {
                await sensaAccountPage.mssgcomparisionremovelinespace(camelFooterlinkPageObject.hdrcamelPoints_camel, hdrcamelPoints);
            }
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblcamelPointsparara1_camel, lblcamelPointsparara1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblcamelPointsparara2_camel, lblcamelPointsparara2);

            const lbleligibility = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbleligibility');
            const lbljoiningpgr = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbljoiningpgr');
            const lblhowewecommunicate = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowewecommunicate');
            const lblhowtoearn = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearn');
            const lblhowtoredeem = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoredeem');
            const lblhowtoavoid = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoavoid');
            const lblavailablerwards = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblavailablerwards');
            const lblreserved = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblreserved');
            const lblprogram = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogram');
            const lbladditional = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbladditional');
            const lblclosing = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblclosing');
            const lbldisclaimer = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbldisclaimer');
            const lblchanges = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblchanges');
            const lblprivacy = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacy');
            const lblindemnity = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblindemnity');
            const lblchoiceoflaw = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblchoiceoflaw');
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbleligibility_camel, lbleligibility);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbljoiningpgr_camel, lbljoiningpgr);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowewecommunicate_camel, lblhowewecommunicate);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearn_camel, lblhowtoearn);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoredeem_camel, lblhowtoredeem);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoavoid_camel, lblhowtoavoid);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblavailablerwards_camel, lblavailablerwards);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblreserved_camel, lblreserved);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogram_camel, lblprogram);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbladditional_camel, lbladditional);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblclosing_camel, lblclosing);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbldisclaimer_camel, lbldisclaimer);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblchanges_camel, lblchanges);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacy_camel, lblprivacy);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblindemnity_camel, lblindemnity);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblchoiceoflaw_camel, lblchoiceoflaw);

            const lbleligibilitydesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbleligibilitydesc1');
            const lbleligibilitydesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbleligibilitydesc2');
            const lbleligibilitydesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbleligibilitydesc3');
            const lbleligibilitydesc4 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbleligibilitydesc4');
            const lbleligibilitydesc5 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbleligibilitydesc5');
            const lbljoiningpgrdesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbljoiningpgrdesc1');
            const lbljoiningpgrdesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbljoiningpgrdesc2');
            const lbljoiningpgrdesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbljoiningpgrdesc3');
            const lblhowewecommunicatedesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowewecommunicatedesc1');
            const lblhowewecommunicatedesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowewecommunicatedesc2');
            const lblhowewecommunicatedesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowewecommunicatedesc3');
            const lblhowtoearndesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc1');
            const lblhowtoearndesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc2');
            const lblhowtoearndesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc3');
            const lblhowtoearndesc4 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc4');
            const lblhowtoearndesc5 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc5');
            const lblhowtoearndesc6 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc6');
            const lblhowtoearndesc7 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc7');
            const lblhowtoearndesc8 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc8');
            const lblhowtoearndesc9 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc9');
            const lblhowtoearndesc10 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc10');
            const lblhowtoearndesc11 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc11');
            const lblhowtoearndesc12 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc12');
            const lblhowtoearndesc13 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoearndesc13');
            const lblhowtoredeemdesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoredeemdesc1');
            const lblhowtoredeemdesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoredeemdesc2');
            const lblhowtoavoiddesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoavoiddesc1');
            const lblhowtoavoiddesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoavoiddesc2');
            const lblhowtoavoiddesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblhowtoavoiddesc3');
            const lblavailablerwardsdesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblavailablerwardsdesc1');
            const lblavailablerwardsdesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblavailablerwardsdesc2');
            const lblavailablerwardsdesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblavailablerwardsdesc3');
            const lblavailablerwardsdesc4 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblavailablerwardsdesc4');
            const lblavailablerwardsdesc5 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblavailablerwardsdesc5');
            const lblavailablerwardsdesc6 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblavailablerwardsdesc6');
            const lblavailablerwardsdesc7 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblavailablerwardsdesc7');
            const lblreserveddesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblreserveddesc1');
            const lblreserveddesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblreserveddesc2');
            const lblprogramdesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc1');
            const lblprogramdesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc2');
            const lblprogramdesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc3');
            const lblprogramdesc4 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc4');
            const lblprogramdesc5 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc5');
            const lblprogramdesc6 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc6');
            const lblprogramdesc7 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc7');
            const lblprogramdesc8 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc8');
            const lblprogramdesc9 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc9');
            const lblprogramdesc10 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprogramdesc10');
            const lbladditionaldesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbladditionaldesc1');
            const lbladditionaldesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbladditionaldesc2');
            const lbladditionaldesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbladditionaldesc3');
            const lbladditionaldesc4 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbladditionaldesc4');
            const lbladditionaldesc5 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbladditionaldesc5');
            const lbladditionaldesc6 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbladditionaldesc6');
            const lblclosingdesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblclosingdesc1');
            const lblclosingdesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblclosingdesc2');
            const lblclosingdesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblclosingdesc3');
            const lblclosingdesc4 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblclosingdesc4');
            const lbldisclaimerdesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbldisclaimerdesc1');
            const lbldisclaimerdesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbldisclaimerdesc2');
            const lbldisclaimerdesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbldisclaimerdesc3');
            const lbldisclaimerdesc4 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lbldisclaimerdesc4');
            const lblchangesdesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblchangesdesc1');
            const lblchangesdesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblchangesdesc2');
            const lblprivacydesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacydesc1');
            const lblprivacydesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacydesc2');
            const lblprivacydesc3 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacydesc3');
            const lblprivacydesc4 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacydesc4');
            const lblprivacydesc5 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacydesc5');
            const lblprivacydesc6 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacydesc6');
            const lblprivacydesc7 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacydesc7');
            const lblprivacydesc8 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblprivacydesc8');
            const lblindemnitydesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblindemnitydesc1');
            const lblindemnitydesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblindemnitydesc2');
            const lblchoiceoflawdesc1 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblchoiceoflawdesc1');
            const lblchoiceoflawdesc2 = testData.getCellValue(SHEET_NAME, _scenarioname, 'lblchoiceoflawdesc2');
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbleligibilitydesc1_camel, lbleligibilitydesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbleligibilitydesc2_camel, lbleligibilitydesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbleligibilitydesc3_camel, lbleligibilitydesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbleligibilitydesc4_camel, lbleligibilitydesc4);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbleligibilitydesc5_camel, lbleligibilitydesc5);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbljoiningpgrdesc1_camel, lbljoiningpgrdesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbljoiningpgrdesc2_camel, lbljoiningpgrdesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbljoiningpgrdesc3_camel, lbljoiningpgrdesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowewecommunicatedesc1_camel, lblhowewecommunicatedesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowewecommunicatedesc2_camel, lblhowewecommunicatedesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowewecommunicatedesc3_camel, lblhowewecommunicatedesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc1_camel, lblhowtoearndesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc2_camel, lblhowtoearndesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc3_camel, lblhowtoearndesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc4_camel, lblhowtoearndesc4);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc5_camel, lblhowtoearndesc5);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc6_camel, lblhowtoearndesc6);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc7_camel, lblhowtoearndesc7);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc8_camel, lblhowtoearndesc8);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc9_camel, lblhowtoearndesc9);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc10_camel, lblhowtoearndesc10);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc11_camel, lblhowtoearndesc11);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc12_camel, lblhowtoearndesc12);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoearndesc13_camel, lblhowtoearndesc13);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoredeemdesc1_camel, lblhowtoredeemdesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoredeemdesc2_camel, lblhowtoredeemdesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoavoiddesc1_camel, lblhowtoavoiddesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoavoiddesc2_camel, lblhowtoavoiddesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblhowtoavoiddesc3_camel, lblhowtoavoiddesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblavailablerwardsdesc1_camel, lblavailablerwardsdesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblavailablerwardsdesc2_camel, lblavailablerwardsdesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblavailablerwardsdesc3_camel, lblavailablerwardsdesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblavailablerwardsdesc4_camel, lblavailablerwardsdesc4);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblavailablerwardsdesc5_camel, lblavailablerwardsdesc5);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblavailablerwardsdesc6_camel, lblavailablerwardsdesc6);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblavailablerwardsdesc7_camel, lblavailablerwardsdesc7);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblreserveddesc1_camel, lblreserveddesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblreserveddesc2_camel, lblreserveddesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc1_camel, lblprogramdesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc2_camel, lblprogramdesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc3_camel, lblprogramdesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc4_camel, lblprogramdesc4);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc5_camel, lblprogramdesc5);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc6_camel, lblprogramdesc6);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc7_camel, lblprogramdesc7);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc8_camel, lblprogramdesc8);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc9_camel, lblprogramdesc9);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprogramdesc10_camel, lblprogramdesc10);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbladditionaldesc1_camel, lbladditionaldesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbladditionaldesc2_camel, lbladditionaldesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbladditionaldesc3_camel, lbladditionaldesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbladditionaldesc4_camel, lbladditionaldesc4);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbladditionaldesc5_camel, lbladditionaldesc5);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbladditionaldesc6_camel, lbladditionaldesc6);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblclosingdesc1_camel, lblclosingdesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblclosingdesc2_camel, lblclosingdesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblclosingdesc3_camel, lblclosingdesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblclosingdesc4_camel, lblclosingdesc4);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbldisclaimerdesc1_camel, lbldisclaimerdesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbldisclaimerdesc2_camel, lbldisclaimerdesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbldisclaimerdesc3_camel, lbldisclaimerdesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lbldisclaimerdesc4_camel, lbldisclaimerdesc4);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblchangesdesc1_camel, lblchangesdesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblchangesdesc2_camel, lblchangesdesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacydesc1_camel, lblprivacydesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacydesc2_camel, lblprivacydesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacydesc3_camel, lblprivacydesc3);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacydesc4_camel, lblprivacydesc4);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacydesc5_camel, lblprivacydesc5);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacydesc6_camel, lblprivacydesc6);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacydesc7_camel, lblprivacydesc7);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblprivacydesc8_camel, lblprivacydesc8);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblindemnitydesc1_camel, lblindemnitydesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblindemnitydesc2_camel, lblindemnitydesc2);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblchoiceoflawdesc1_camel, lblchoiceoflawdesc1);
            await sensaAccountPage.mssgcomparision(camelFooterlinkPageObject.lblchoiceoflawdesc2_camel, lblchoiceoflawdesc2);
            await this.footerlinkoneachpage();
            console.log('Validated the Content in Camel Points page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the content in  Camel Points page', { error });
            throw error;
        }
    }


    async clickonsustainabilityprefooterlink() {
        try {
            const url = await browser.getUrl();
            if (url.includes('aem')) {
                await elementActions.clickusingJavascript(camelLoginPageObject.lnksustainability_camel);
            } else {
                await elementActions.clickusingJavascript(camelFooterlinkPageObject.lnksustainabilitypostlogin_camel);
            }
            await this.allowpopup();
            await this.windowswitchandback();
            if (url.includes('aem')) {
                await elementActions.clickElementIfExistsAndVisible(camelFooterlinkPageObject.btnacceptCookies_camel);
            }
            await elementActions.waitForDisplayed(camelFooterlinkPageObject.hdrsustainability_camel);
            await elementActions.assertion(camelFooterlinkPageObject.hdrsustainability_camel);
            console.log('Navigated to Sustainability Page Successfully');
        } catch (error) {
            logger.error('Failed to Navigate to Sustainability Page', { error });
            throw error;
        }
    }

    async NasFAQpageValidation(filename: string, sheetname: string, scenarioname: string) {
        try {

            const SHEET_NAME = sheetname;
            const jsonFilePath = path.join(process.cwd(), 'data', filename);
            const testData = new JsonTestDataHandler(jsonFilePath);
            const userData1 = testData.getTestCaseData(SHEET_NAME, scenarioname);
            console.log(`User Data for TC01: ${JSON.stringify(userData1)}`);
            const contactus = testData.getCellValue(SHEET_NAME, scenarioname, 'hdrcontactUs');
            const lblfurtherassistance = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfurtherassistance');
            const lblfurtherquestion = testData.getCellValue(SHEET_NAME, scenarioname, 'lblfurtherquestion');
            const lblonthispage = testData.getCellValue(SHEET_NAME, scenarioname, 'lblonthispage');
            const url = await browser.getUrl();
            if (url.includes('secure')) {
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.postcontactUsHeader, contactus);
            } else {
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.contactUsHeader, contactus);
            }
            await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblfurtherassistance, lblfurtherassistance);
            await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblfrequentQuestions, lblfurtherquestion);
            await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblonthisPage_nas, lblonthispage);
            const hdrlogin = testData.getCellValue(SHEET_NAME, scenarioname, 'lblwarnings');
            const lblQuestion1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion1');
            const lblQuestion2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion2');
            const lblQuestion3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion3');
            const lblQuestion4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion4');
            const lblQuestion5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion5');
            const hdroffers = testData.getCellValue(SHEET_NAME, scenarioname, 'lbloffersandpromotions');
            const lblQuestion6 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion6');
            const lblQuestion7 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion7');
            const hdrageverification = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAge');
            const lblQuestion11 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion11');
            const lblQuestion12 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion12');
            const hdrprivacy = testData.getCellValue(SHEET_NAME, scenarioname, 'lblprivacy');
            const lblQuestion13 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion13');
            const lblQuestion14 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion14');
            const hdrtroubleshooting = testData.getCellValue(SHEET_NAME, scenarioname, 'lbltroubleshooting');
            const lblQuestion15 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion15');
            const lblQuestion16 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion16');
            const lblQuestion17 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion17');
            const lblQuestion18 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion18');
            const lblQuestion19 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion19');
            const lblQuestion20 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblQuestion20');

            const lblAnswer1 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer1');
            const lblAnswer2 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer2');
            const lblAnswer3 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer3');
            const lblAnswer4 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer4');
            const lblAnswer5 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer5');
            const lblAnswer6 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer6');
            const lblAnswer7 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer7');
            const lblAnswer11 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer11');
            const lblAnswer12 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer12');
            const lblAnswer13 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer13');
            const lblAnswer14 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer14');
            const lblAnswer15 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer15');
            const lblAnswer16 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer16');
            const lblAnswer17 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer17');
            const lblAnswer18 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer18');
            const lblAnswer19 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer19');
            const lblAnswer20 = testData.getCellValue(SHEET_NAME, scenarioname, 'lblAnswer20');

            const lblproduct = testData.getCellValue(SHEET_NAME, scenarioname, 'lblproduct');
            if (url.includes('secure')) {
                
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblproducts_nas, lblproduct);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ1_nas, lblQuestion18);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA1_nas, lblAnswer18);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ2_nas, lblQuestion19);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ2_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA2_nas, lblAnswer19);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ3_nas, lblQuestion20);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ3_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA3_nas, lblAnswer20);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblLoginAndPassword_nas, hdrlogin);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ4_nas, lblQuestion1);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA4_nas, lblAnswer1);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ5_nas, lblQuestion2);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ5_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA5_nas, lblAnswer2);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ6_nas, lblQuestion3);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ6_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA6_nas, lblAnswer3);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ7_nas, lblQuestion4);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ7_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA7_nas, lblAnswer4);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ8_nas, lblQuestion5);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ8_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA8_nas, lblAnswer5);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblOffersAndPromotions_nas, hdroffers);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ9_nas, lblQuestion6);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA9_nas, lblAnswer6);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ10_nas, lblQuestion7);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ10_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA10_nas, lblAnswer7);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblAgeVerification_nas, hdrageverification);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ11_nas, lblQuestion11);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA11_nas, lblAnswer11);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ12_nas, lblQuestion12);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ12_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA12_nas, lblAnswer12);
                await elementActions.assertion(NasFooterlinkPageObject.lblPrivacy_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ13_nas, lblQuestion13);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA13_nas, lblAnswer13);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ14_nas, lblQuestion14);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ14_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA14_nas, lblAnswer14);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblTroubleshooting_nas, hdrtroubleshooting);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ15_nas, lblQuestion15);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA15_nas, lblAnswer15);
                await elementActions.assertion(NasFooterlinkPageObject.listFaqSection1_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ16_nas, lblQuestion16);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ16_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA16_nas, lblAnswer16);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ17_nas, lblQuestion17);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ17_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA17_nas, lblAnswer17);
                await elementActions.assertion(NasFooterlinkPageObject.listFaqSection2_nas);
                await elementActions.assertion(NasFooterlinkPageObject.lblNeedFurtherAssistance_nas);
                await elementActions.assertion(NasFooterlinkPageObject.lblHaveQuestions_nas);
                await elementActions.assertion(NasFooterlinkPageObject.listCmpText3_nas);
                
                await browser.back();
            } else {
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblproducts_nas, lblproduct);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ1_nas, lblQuestion18);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA1_nas, lblAnswer18);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ2_nas, lblQuestion19);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ2_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA2_nas, lblAnswer19);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ3_nas, lblQuestion20);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ3_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA3_nas, lblAnswer20);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblLoginAndPassword_nas, hdrlogin);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ4_nas, lblQuestion1);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA4_nas, lblAnswer1);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ5_nas, lblQuestion2);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ5_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA5_nas, lblAnswer2);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ6_nas, lblQuestion3);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ6_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA6_nas, lblAnswer3);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ7_nas, lblQuestion4);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ7_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA7_nas, lblAnswer4);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ8_nas, lblQuestion5);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ8_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA8_nas, lblAnswer5);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblOffersAndPromotions_nas, hdroffers);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ9_nas, lblQuestion6);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA9_nas, lblAnswer6);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ10_nas, lblQuestion7);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ10_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA10_nas, lblAnswer7);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblAgeVerification_nas, hdrageverification);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ11_nas, lblQuestion11);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA11_nas, lblAnswer11);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ12_nas, lblQuestion12);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ12_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA12_nas, lblAnswer12);
                await elementActions.assertion(NasFooterlinkPageObject.lblPrivacy_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ13_nas, lblQuestion13);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA13_nas, lblAnswer13);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ14_nas, lblQuestion14);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ14_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA14_nas, lblAnswer14);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblTroubleshooting_nas, hdrtroubleshooting);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ15_nas, lblQuestion15);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA15_nas, lblAnswer15);
                await elementActions.assertion(NasFooterlinkPageObject.listFaqSection1_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ16_nas, lblQuestion16);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ16_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA16_nas, lblAnswer16);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningQ17_nas, lblQuestion17);
                await elementActions.click(NasFooterlinkPageObject.lblwarningQ17_nas);
                await sensaAccountPage.mssgcomparision(NasFooterlinkPageObject.lblwarningA17_nas, lblAnswer17);
                await elementActions.assertion(NasFooterlinkPageObject.listFaqSection2_nas);
                await elementActions.assertion(NasFooterlinkPageObject.lblNeedFurtherAssistance_nas);
                await elementActions.assertion(NasFooterlinkPageObject.lblHaveQuestions_nas);
                await elementActions.assertion(NasFooterlinkPageObject.listCmpText3_nas);
                await this.footerlinkoneachpage();
            }
            
            console.log('Validated the Content in FAQ page Successfully');
        } catch (error) {
            logger.error('Failed to Validate the Content in FAQ page', { error });
            throw error;
        }
    }


}
export default new FooterlinksPage();
